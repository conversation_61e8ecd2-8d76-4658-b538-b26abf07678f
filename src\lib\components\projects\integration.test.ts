/**
 * Project Management Integration Tests for TimeFlow Pro
 * 
 * Tests the integration between project stores, services, and components
 * to ensure the complete project management system works correctly.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { get } from 'svelte/store';
import { 
	projects, 
	createProject, 
	updateProject, 
	deleteProject,
	activeProjects,
	projectColorMap
} from '$lib/stores/projects.js';
import { 
	tasks, 
	createTask, 
	updateTask, 
	deleteTask,
	activeTasks,
	tasksByProject
} from '$lib/stores/tasks.js';
import { ProjectService } from '$lib/services/project-service.js';
import { TaskService } from '$lib/services/task-service.js';
import { 
	generateProjectSummary,
	generateTaskSummary,
	calculateProjectProgress
} from '$lib/utils/progress-utils.js';
import type { Project, Task, TimeEntry } from '$lib/types/index.js';

describe('Project Management Integration', () => {
	let projectService: ProjectService;
	let taskService: TaskService;

	beforeEach(() => {
		projectService = new ProjectService();
		taskService = new TaskService();
		
		// Clear stores
		projects.set([]);
		tasks.set([]);
	});

	describe('Project and Task Store Integration', () => {
		it('should create project and associated tasks', () => {
			// Create a project
			const projectId = createProject({
				name: 'Integration Test Project',
				clientName: 'Test Client',
				defaultBillingRate: 100,
				colorCode: '#3B82F6',
				status: 'active',
				actualHours: 0,
				tags: [],
				isBillable: true,
				isArchived: false
			});

			// Verify project was created
			const allProjects = get(projects);
			expect(allProjects).toHaveLength(1);
			expect(allProjects[0].id).toBe(projectId);
			expect(allProjects[0].name).toBe('Integration Test Project');

			// Create tasks for the project
			const task1Id = createTask({
				projectId,
				title: 'Task 1',
				priority: 'high',
				status: 'pending',
				estimatedHours: 8,
				actualHours: 0,
				tags: [],
				completionPercentage: 0,
				isBillable: true
			});

			const task2Id = createTask({
				projectId,
				title: 'Task 2',
				priority: 'medium',
				status: 'in-progress',
				estimatedHours: 4,
				actualHours: 0,
				tags: [],
				completionPercentage: 25,
				isBillable: true
			});

			// Verify tasks were created
			const allTasks = get(tasks);
			expect(allTasks).toHaveLength(2);
			expect(allTasks.every(task => task.projectId === projectId)).toBe(true);

			// Test derived stores
			const activeProjectsList = get(activeProjects);
			expect(activeProjectsList).toHaveLength(1);

			const activeTasksList = get(activeTasks);
			expect(activeTasksList).toHaveLength(2);

			const tasksByProjectMap = get(tasksByProject);
			expect(tasksByProjectMap.get(projectId)).toHaveLength(2);
		});

		it('should update project and reflect changes in derived stores', () => {
			// Create project
			const projectId = createProject({
				name: 'Test Project',
				clientName: 'Test Client',
				defaultBillingRate: 100,
				colorCode: '#3B82F6',
				status: 'active',
				actualHours: 0,
				tags: [],
				isBillable: true,
				isArchived: false
			});

			// Update project status
			updateProject(projectId, { status: 'completed' });

			// Verify update
			const allProjects = get(projects);
			expect(allProjects[0].status).toBe('completed');

			// Verify derived store reflects change
			const activeProjectsList = get(activeProjects);
			expect(activeProjectsList).toHaveLength(0); // No longer active
		});

		it('should delete project and associated tasks', () => {
			// Create project and tasks
			const projectId = createProject({
				name: 'Test Project',
				clientName: 'Test Client',
				defaultBillingRate: 100,
				colorCode: '#3B82F6',
				status: 'active',
				actualHours: 0,
				tags: [],
				isBillable: true,
				isArchived: false
			});

			const taskId = createTask({
				projectId,
				title: 'Test Task',
				priority: 'medium',
				status: 'pending',
				actualHours: 0,
				tags: [],
				completionPercentage: 0,
				isBillable: true
			});

			// Verify initial state
			expect(get(projects)).toHaveLength(1);
			expect(get(tasks)).toHaveLength(1);

			// Delete project
			deleteProject(projectId);

			// Verify project was deleted
			expect(get(projects)).toHaveLength(0);

			// Note: In a real implementation, we would also delete associated tasks
			// For now, we'll manually delete the task to simulate this behavior
			deleteTask(taskId);
			expect(get(tasks)).toHaveLength(0);
		});
	});

	describe('Service Integration', () => {
		it('should validate project data using service', () => {
			const validProjectData = {
				name: 'Valid Project',
				clientName: 'Valid Client',
				defaultBillingRate: 100,
				colorCode: '#3B82F6'
			};

			const validation = projectService.validateProject(validProjectData);
			expect(validation.isValid).toBe(true);

			// Should be able to create project with valid data
			expect(() => {
				createProject({
					...validProjectData,
					status: 'active',
					actualHours: 0,
					tags: [],
					isBillable: true,
					isArchived: false
				});
			}).not.toThrow();
		});

		it('should filter and sort projects using service', () => {
			// Create multiple projects
			createProject({
				name: 'Project A',
				clientName: 'Client 1',
				defaultBillingRate: 100,
				colorCode: '#3B82F6',
				status: 'active',
				actualHours: 0,
				tags: ['web'],
				isBillable: true,
				isArchived: false
			});

			createProject({
				name: 'Project B',
				clientName: 'Client 2',
				defaultBillingRate: 150,
				colorCode: '#10B981',
				status: 'completed',
				actualHours: 0,
				tags: ['mobile'],
				isBillable: true,
				isArchived: false
			});

			const allProjects = get(projects);

			// Test filtering
			const activeOnly = projectService.filterProjects(allProjects, { status: ['active'] });
			expect(activeOnly).toHaveLength(1);
			expect(activeOnly[0].status).toBe('active');

			// Test sorting
			const sortedByName = projectService.sortProjects(allProjects, 'name', 'asc');
			expect(sortedByName[0].name).toBe('Project A');
			expect(sortedByName[1].name).toBe('Project B');
		});
	});

	describe('Progress Calculation Integration', () => {
		it('should calculate project progress based on tasks', () => {
			// Create project
			const projectId = createProject({
				name: 'Progress Test Project',
				clientName: 'Test Client',
				defaultBillingRate: 100,
				colorCode: '#3B82F6',
				status: 'active',
				estimatedHours: 20,
				actualHours: 0,
				tags: [],
				isBillable: true,
				isArchived: false
			});

			// Create tasks
			createTask({
				projectId,
				title: 'Completed Task',
				status: 'completed',
				priority: 'medium',
				actualHours: 0,
				tags: [],
				completionPercentage: 100,
				isBillable: true
			});

			createTask({
				projectId,
				title: 'Pending Task',
				status: 'pending',
				priority: 'medium',
				actualHours: 0,
				tags: [],
				completionPercentage: 0,
				isBillable: true
			});

			const project = get(projects)[0];
			const projectTasks = get(tasks);
			const timeEntries: TimeEntry[] = [];

			// Calculate progress
			const progress = calculateProjectProgress(project, projectTasks, timeEntries);
			expect(progress).toBe(50); // 1 out of 2 tasks completed

			// Generate project summary
			const summary = generateProjectSummary(project, projectTasks, timeEntries);
			expect(summary.taskCount).toBe(2);
			expect(summary.completedTasks).toBe(1);
			expect(summary.progressPercentage).toBe(50);
		});
	});

	describe('Component Data Flow', () => {
		it('should provide correct data structure for components', () => {
			// Create project and tasks
			const projectId = createProject({
				name: 'Component Test Project',
				clientName: 'Test Client',
				defaultBillingRate: 100,
				colorCode: '#3B82F6',
				status: 'active',
				actualHours: 0,
				tags: ['test'],
				isBillable: true,
				isArchived: false
			});

			const taskId = createTask({
				projectId,
				title: 'Component Test Task',
				priority: 'high',
				status: 'in-progress',
				estimatedHours: 8,
				actualHours: 0,
				tags: ['frontend'],
				completionPercentage: 30,
				isBillable: true
			});

			// Get data as components would
			const allProjects = get(projects);
			const allTasks = get(tasks);
			const colorMap = get(projectColorMap);

			// Verify data structure
			expect(allProjects[0]).toHaveProperty('id');
			expect(allProjects[0]).toHaveProperty('name');
			expect(allProjects[0]).toHaveProperty('clientName');
			expect(allProjects[0]).toHaveProperty('colorCode');

			expect(allTasks[0]).toHaveProperty('id');
			expect(allTasks[0]).toHaveProperty('projectId');
			expect(allTasks[0]).toHaveProperty('title');
			expect(allTasks[0]).toHaveProperty('priority');
			expect(allTasks[0]).toHaveProperty('status');

			expect(colorMap.get(projectId)).toBe('#3B82F6');

			// Generate summaries as components would
			const projectSummary = generateProjectSummary(allProjects[0], allTasks, []);
			const taskSummary = generateTaskSummary(allTasks[0], [], allProjects[0]);

			expect(projectSummary).toHaveProperty('project');
			expect(projectSummary).toHaveProperty('totalHours');
			expect(projectSummary).toHaveProperty('progressPercentage');

			expect(taskSummary).toHaveProperty('task');
			expect(taskSummary).toHaveProperty('totalTime');
			expect(taskSummary).toHaveProperty('progressPercentage');
		});
	});
});
